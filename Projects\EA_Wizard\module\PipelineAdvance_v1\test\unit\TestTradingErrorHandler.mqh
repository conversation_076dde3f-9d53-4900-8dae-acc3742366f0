#property strict

//+------------------------------------------------------------------+
//| TestTradingErrorHandler.mqh                                      |
//| TradingErrorHandler 類的單元測試                                 |
//+------------------------------------------------------------------+

#include "../TestFramework.mqh"
#include "../../TradingErrorHandler.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| Mock 訪問者類 - 用於測試                                        |
//+------------------------------------------------------------------+
class MockErrorVisitor : public TradingMessageVisitor
{
private:
    int m_visitCount;
    string m_lastMessage;
    ENUM_ERROR_LEVEL m_lastLevel;

public:
    MockErrorVisitor() : m_visitCount(0), m_lastMessage(""), m_lastLevel(ERROR_LEVEL_INFO) {}

    virtual void Visit(const TradingMessageRecord& record) override
    {
        m_visitCount++;
        m_lastMessage = record.m_message;
        m_lastLevel = record.m_errorLevel;
    }

    int GetVisitCount() const { return m_visitCount; }
    string GetLastMessage() const { return m_lastMessage; }
    ENUM_ERROR_LEVEL GetLastLevel() const { return m_lastLevel; }
    void Reset() { m_visitCount = 0; m_lastMessage = ""; m_lastLevel = ERROR_LEVEL_INFO; }
};

//+------------------------------------------------------------------+
//| TradingErrorHandler 單元測試類                                  |
//+------------------------------------------------------------------+
class TestTradingErrorHandler : public TestCase
{
private:
    TestRunner* m_runner;

public:
    TestTradingErrorHandler(TestRunner* runner = NULL)
        : TestCase("TestTradingErrorHandler"), m_runner(runner) {}

    virtual void RunTests() override
    {
        Print("開始執行 TradingErrorHandler 單元測試...");

        TestConstructorAndBasicProperties();
        TestAddError();
        TestErrorManagement();
        TestVisitorPattern();
        TestPipelineResultHandling();
        TestErrorStatistics();
        TestErrorLevelFiltering();
        TestMaxErrorsLimit();

        Print("TradingErrorHandler 單元測試完成");
    }

private:
    // 測試構造函數和基本屬性
    void TestConstructorAndBasicProperties()
    {
        Print("--- 測試 TradingErrorHandler 構造函數和基本屬性 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestConstructorAndBasicProperties - 初始狀態為空",
                handler.IsEmpty(),
                "初始狀態應該為空"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestConstructorAndBasicProperties - 初始錯誤數量為0",
                handler.GetErrorCount() == 0,
                "初始錯誤數量應該為0"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestConstructorAndBasicProperties - 初始無嚴重錯誤",
                !handler.HasCriticalErrors(),
                "初始狀態不應該有嚴重錯誤"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestConstructorAndBasicProperties - 初始最後錯誤為空",
                handler.GetLastError() == "",
                "初始最後錯誤應該為空字符串"
            ));
        }

        delete handler;
    }

    // 測試添加錯誤
    void TestAddError()
    {
        Print("--- 測試 TradingErrorHandler 添加錯誤 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        // 添加第一個錯誤
        handler.AddError("測試錯誤1", "TestSource", ERROR_LEVEL_WARNING);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddError - 錯誤數量增加",
                handler.GetErrorCount() == 1,
                "添加錯誤後數量應該為1"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddError - 不再為空",
                !handler.IsEmpty(),
                "添加錯誤後不應該為空"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddError - 最後錯誤正確",
                handler.GetLastError() == "測試錯誤1",
                "最後錯誤消息應該正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddError - 第一個錯誤正確",
                handler.GetFirstError() == "測試錯誤1",
                "第一個錯誤消息應該正確"
            ));
        }

        // 添加第二個錯誤
        handler.AddError("測試錯誤2", "TestSource2", ERROR_LEVEL_ERROR);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddError - 多個錯誤數量",
                handler.GetErrorCount() == 2,
                "添加第二個錯誤後數量應該為2"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddError - 最後錯誤更新",
                handler.GetLastError() == "測試錯誤2",
                "最後錯誤應該更新為最新的"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddError - 第一個錯誤保持",
                handler.GetFirstError() == "測試錯誤1",
                "第一個錯誤應該保持不變"
            ));
        }

        delete handler;
    }

    // 測試錯誤管理功能
    void TestErrorManagement()
    {
        Print("--- 測試 TradingErrorHandler 錯誤管理 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        // 添加多個錯誤
        handler.AddError("錯誤1", "Source1", ERROR_LEVEL_INFO);
        handler.AddError("錯誤2", "Source2", ERROR_LEVEL_WARNING);
        handler.AddError("錯誤3", "Source3", ERROR_LEVEL_ERROR);

        // 測試 PopError
        string poppedError = handler.PopErrorString();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorManagement - PopError返回正確",
                poppedError == "錯誤3",
                "PopError應該返回最後一個錯誤"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorManagement - PopError後數量減少",
                handler.GetErrorCount() == 2,
                "PopError後錯誤數量應該減少"
            ));
        }

        // 測試 ShiftError
        string shiftedError = handler.ShiftErrorString();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorManagement - ShiftError返回正確",
                shiftedError == "錯誤1",
                "ShiftError應該返回第一個錯誤"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorManagement - ShiftError後數量減少",
                handler.GetErrorCount() == 1,
                "ShiftError後錯誤數量應該減少"
            ));
        }

        // 測試 ClearErrors
        handler.ClearErrors();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorManagement - ClearErrors清空",
                handler.IsEmpty(),
                "ClearErrors後應該為空"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorManagement - ClearErrors數量為0",
                handler.GetErrorCount() == 0,
                "ClearErrors後數量應該為0"
            ));
        }

        delete handler;
    }

    // 測試訪問者模式
    void TestVisitorPattern()
    {
        Print("--- 測試 TradingErrorHandler 訪問者模式 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();
        MockErrorVisitor* visitor = new MockErrorVisitor();

        // 添加錯誤
        handler.AddError("訪問者測試錯誤", "VisitorTest", ERROR_LEVEL_CRITICAL);

        // 使用訪問者處理錯誤
        handler.HandleError(visitor);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestVisitorPattern - 訪問次數正確",
                visitor.GetVisitCount() == 1,
                "訪問者應該被調用一次"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestVisitorPattern - 訪問消息正確",
                visitor.GetLastMessage() == "訪問者測試錯誤",
                "訪問者應該收到正確的錯誤消息"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestVisitorPattern - 訪問級別正確",
                visitor.GetLastLevel() == ERROR_LEVEL_CRITICAL,
                "訪問者應該收到正確的錯誤級別"
            ));
        }

        delete visitor;
        delete handler;
    }

    // 測試 PipelineResult 處理
    void TestPipelineResultHandling()
    {
        Print("--- 測試 TradingErrorHandler PipelineResult 處理 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        // 創建失敗的 PipelineResult
        PipelineResult* failedResult = new PipelineResult(false, "流水線執行失敗", "TestPipeline", ERROR_LEVEL_ERROR);

        // 處理 PipelineResult
        handler.HandlePipelineResult(failedResult);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestPipelineResultHandling - 處理失敗結果",
                handler.GetErrorCount() == 1,
                "處理失敗的PipelineResult應該添加錯誤"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestPipelineResultHandling - 錯誤消息正確",
                handler.GetLastError() == "流水線執行失敗",
                "錯誤消息應該來自PipelineResult"
            ));
        }

        // 創建成功的 PipelineResult
        PipelineResult* successResult = new PipelineResult(true, "流水線執行成功", "TestPipeline", ERROR_LEVEL_INFO);

        // 處理成功的結果（不應該添加錯誤）
        handler.HandlePipelineResult(successResult);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestPipelineResultHandling - 忽略成功結果",
                handler.GetErrorCount() == 1,
                "處理成功的PipelineResult不應該添加錯誤"
            ));
        }

        delete failedResult;
        delete successResult;
        delete handler;
    }

    // 測試錯誤統計
    void TestErrorStatistics()
    {
        Print("--- 測試 TradingErrorHandler 錯誤統計 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        // 添加不同級別的錯誤
        handler.AddError("信息", "Test", ERROR_LEVEL_INFO);
        handler.AddError("警告", "Test", ERROR_LEVEL_WARNING);
        handler.AddError("錯誤", "Test", ERROR_LEVEL_ERROR);
        handler.AddError("嚴重", "Test", ERROR_LEVEL_CRITICAL);
        handler.AddError("另一個錯誤", "Test", ERROR_LEVEL_ERROR);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorStatistics - INFO級別統計",
                handler.GetErrorCountByLevel(ERROR_LEVEL_INFO) == 1,
                "INFO級別錯誤數量應該為1"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorStatistics - WARNING級別統計",
                handler.GetErrorCountByLevel(ERROR_LEVEL_WARNING) == 1,
                "WARNING級別錯誤數量應該為1"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorStatistics - ERROR級別統計",
                handler.GetErrorCountByLevel(ERROR_LEVEL_ERROR) == 2,
                "ERROR級別錯誤數量應該為2"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorStatistics - CRITICAL級別統計",
                handler.GetErrorCountByLevel(ERROR_LEVEL_CRITICAL) == 1,
                "CRITICAL級別錯誤數量應該為1"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorStatistics - 有嚴重錯誤",
                handler.HasCriticalErrors(),
                "應該檢測到嚴重錯誤"
            ));
        }

        delete handler;
    }

    // 測試錯誤級別過濾
    void TestErrorLevelFiltering()
    {
        Print("--- 測試 TradingErrorHandler 錯誤級別過濾 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();
        MockErrorVisitor* targetVisitor = new MockErrorVisitor();
        MessageFilterVisitor* filterVisitor = new MessageFilterVisitor(ERROR_LEVEL_ERROR, targetVisitor);

        // 添加不同級別的錯誤
        handler.AddError("信息錯誤", "Test", ERROR_LEVEL_INFO);
        handler.AddError("普通錯誤", "Test", ERROR_LEVEL_ERROR);
        handler.AddError("警告錯誤", "Test", ERROR_LEVEL_WARNING);

        // 使用過濾訪問者（只處理ERROR級別）
        handler.HandleError(filterVisitor);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorLevelFiltering - 過濾器只處理指定級別",
                targetVisitor.GetVisitCount() == 1,
                "過濾器應該只處理ERROR級別的錯誤"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestErrorLevelFiltering - 過濾器消息正確",
                targetVisitor.GetLastMessage() == "普通錯誤",
                "過濾器應該傳遞正確的錯誤消息"
            ));
        }

        delete filterVisitor;
        delete targetVisitor;
        delete handler;
    }

    // 測試最大錯誤數量限制
    void TestMaxErrorsLimit()
    {
        Print("--- 測試 TradingErrorHandler 最大錯誤數量限制 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        // 設置較小的最大錯誤數量
        handler.SetMaxMessages(3);

        // 添加超過限制的錯誤
        handler.AddError("錯誤1", "Test", ERROR_LEVEL_INFO);
        handler.AddError("錯誤2", "Test", ERROR_LEVEL_INFO);
        handler.AddError("錯誤3", "Test", ERROR_LEVEL_INFO);
        handler.AddError("錯誤4", "Test", ERROR_LEVEL_INFO);  // 應該替換最舊的錯誤

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMaxErrorsLimit - 錯誤數量限制",
                handler.GetErrorCount() == 3,
                "錯誤數量應該不超過設定的最大值"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMaxErrorsLimit - 最舊錯誤被替換",
                handler.GetFirstError() == "錯誤2",
                "最舊的錯誤應該被新錯誤替換"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMaxErrorsLimit - 最新錯誤保留",
                handler.GetLastError() == "錯誤4",
                "最新的錯誤應該被保留"
            ));
        }

        delete handler;
    }
};
